import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  TextField,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Calculate as CalculateIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { TechnicalIndicatorService } from '../services/api/technicalIndicatorService';
import { InstrumentService } from '../services/api/instrumentService';
import { Instrument, TechnicalIndicatorRequest } from '../types/api';

interface CalculationResult {
  success: boolean;
  message: string;
  details?: any;
}

const TechnicalIndicators: React.FC = () => {
  // State management
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([]);
  const [calculationMode, setCalculationMode] = useState<'INCREMENTAL' | 'FULL_RECALCULATION' | 'SKIP_EXISTING'>('INCREMENTAL');
  const [loadingInstruments, setLoadingInstruments] = useState(false);
  const [calculatingBollinger, setCalculatingBollinger] = useState(false);
  const [calculatingDMI, setCalculatingDMI] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Load instruments on component mount
  useEffect(() => {
    loadInstruments();
  }, []);

  const loadInstruments = async () => {
    setLoadingInstruments(true);
    try {
      // Fetch instruments ordered by market cap (descending)
      const response = await InstrumentService.getInstruments(0, 1000, 'marketCap', 'desc');
      if (response.success && response.data) {
        setInstruments(response.data.content);
      } else {
        showSnackbar('Failed to load instruments', 'error');
      }
    } catch (error: any) {
      console.error('Error loading instruments:', error);
      showSnackbar('Error loading instruments: ' + (error.message || 'Unknown error'), 'error');
    } finally {
      setLoadingInstruments(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleCalculateBollingerBands = async () => {
    setCalculatingBollinger(true);
    try {
      const request: TechnicalIndicatorRequest = {
        mode: calculationMode,
        period: 20,
        stdDevMultiplier: 2.0,
        minDataPoints: 20,
        dryRun: false,
        ...(selectedSymbols.length > 0 && { symbols: selectedSymbols }),
      };

      const response = await TechnicalIndicatorService.calculateBollingerBands(request);

      if (response.success) {
        const details = response.data;
        const message = `Bollinger Bands calculation completed! Processed ${details.processedSymbols || 0} symbols, updated ${details.totalRecordsUpdated || 0} records.`;
        showSnackbar(message, 'success');
      } else {
        showSnackbar(`Bollinger Bands calculation failed: ${response.message}`, 'error');
      }
    } catch (error: any) {
      console.error('Error calculating Bollinger Bands:', error);
      showSnackbar(`Error calculating Bollinger Bands: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setCalculatingBollinger(false);
    }
  };

  const handleCalculateDMI = async () => {
    setCalculatingDMI(true);
    try {
      const request: TechnicalIndicatorRequest = {
        mode: calculationMode,
        period: 14,
        minDataPoints: 28, // DMI needs 2*period for full calculation
        dryRun: false,
        ...(selectedSymbols.length > 0 && { symbols: selectedSymbols }),
      };

      const response = await TechnicalIndicatorService.calculateDMI(request);

      if (response.success) {
        const details = response.data;
        const message = `DMI calculation completed! Processed ${details.processedSymbols || 0} symbols, updated ${details.totalRecordsUpdated || 0} records.`;
        showSnackbar(message, 'success');
      } else {
        showSnackbar(`DMI calculation failed: ${response.message}`, 'error');
      }
    } catch (error: any) {
      console.error('Error calculating DMI:', error);
      showSnackbar(`Error calculating DMI: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setCalculatingDMI(false);
    }
  };

  const getSymbolOptions = () => {
    return instruments.map(instrument => ({
      label: `${instrument.symbol} - ${instrument.name}`,
      value: instrument.symbol,
    }));
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Technical Indicators
      </Typography>

      {/* Configuration Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <SettingsIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">
              Calculation Settings
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {/* Symbol Selection */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                multiple
                options={getSymbolOptions()}
                getOptionLabel={(option) => option.label}
                value={getSymbolOptions().filter(option => selectedSymbols.includes(option.value))}
                onChange={(_, newValue) => {
                  setSelectedSymbols(newValue.map(option => option.value));
                }}
                loading={loadingInstruments}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Symbols"
                    placeholder={selectedSymbols.length === 0 ? "All Symbols" : "Add more symbols..."}
                    helperText={
                      selectedSymbols.length === 0
                        ? "Leave empty to calculate for all symbols"
                        : `${selectedSymbols.length} symbol(s) selected`
                    }
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {loadingInstruments ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.value}
                      size="small"
                      {...getTagProps({ index })}
                      key={option.value}
                    />
                  ))
                }
                disabled={calculatingBollinger || calculatingDMI}
              />
            </Grid>

            {/* Calculation Mode */}
            <Grid item xs={12} md={6}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Calculation Mode</FormLabel>
                <RadioGroup
                  value={calculationMode}
                  onChange={(e) => setCalculationMode(e.target.value as any)}
                  row
                >
                  <FormControlLabel
                    value="INCREMENTAL"
                    control={<Radio size="small" />}
                    label="Incremental"
                    disabled={calculatingBollinger || calculatingDMI}
                  />
                  <FormControlLabel
                    value="FULL_RECALCULATION"
                    control={<Radio size="small" />}
                    label="Full Recalc"
                    disabled={calculatingBollinger || calculatingDMI}
                  />
                  <FormControlLabel
                    value="SKIP_EXISTING"
                    control={<Radio size="small" />}
                    label="Skip Existing"
                    disabled={calculatingBollinger || calculatingDMI}
                  />
                </RadioGroup>
              </FormControl>
            </Grid>
          </Grid>

          {/* Mode Descriptions */}
          <Box mt={2}>
            <Typography variant="caption" color="text.secondary">
              <strong>Incremental:</strong> Calculate only new data points (recommended for regular updates) |{' '}
              <strong>Full Recalc:</strong> Recalculate all data from scratch |{' '}
              <strong>Skip Existing:</strong> Only calculate for symbols without existing data
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Technical Indicators */}
      <Grid container spacing={3}>
        {/* Bollinger Bands */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">
                  Bollinger Bands
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" paragraph>
                Volatility indicator consisting of a middle band (SMA) and upper/lower bands based on standard deviation.
                Uses 20-day period with 2.0 standard deviation multiplier.
              </Typography>

              {calculatingBollinger && (
                <Box mb={2}>
                  <LinearProgress />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    Calculating Bollinger Bands...
                  </Typography>
                </Box>
              )}

              <Button
                variant="contained"
                startIcon={calculatingBollinger ? <CircularProgress size={20} /> : <CalculateIcon />}
                onClick={handleCalculateBollingerBands}
                disabled={calculatingBollinger || calculatingDMI}
                fullWidth
              >
                {calculatingBollinger ? 'Calculating...' : 'Calculate Bollinger Bands'}
              </Button>

              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Target: {selectedSymbols.length > 0 ? `${selectedSymbols.length} selected symbols` : 'All symbols'} |
                  Mode: {calculationMode.replace('_', ' ').toLowerCase()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* DMI */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6">
                  DMI (Directional Movement Index)
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" paragraph>
                Momentum indicator that measures the strength of price movement in positive and negative directions.
                Uses 14-day period and includes +DI, -DI, and ADX calculations.
              </Typography>

              {calculatingDMI && (
                <Box mb={2}>
                  <LinearProgress color="secondary" />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    Calculating DMI indicators...
                  </Typography>
                </Box>
              )}

              <Button
                variant="contained"
                color="secondary"
                startIcon={calculatingDMI ? <CircularProgress size={20} /> : <CalculateIcon />}
                onClick={handleCalculateDMI}
                disabled={calculatingBollinger || calculatingDMI}
                fullWidth
              >
                {calculatingDMI ? 'Calculating...' : 'Calculate DMI'}
              </Button>

              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Target: {selectedSymbols.length > 0 ? `${selectedSymbols.length} selected symbols` : 'All symbols'} |
                  Mode: {calculationMode.replace('_', ' ').toLowerCase()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TechnicalIndicators;
